package com.fasnote.alm.checklist.repository;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.fasnote.alm.checklist.model.ChecklistTemplate;
import com.fasnote.alm.checklist.service.IRepositoryFileService;
import com.fasnote.alm.checklist.util.JsonUtil;
import com.polarion.alm.projects.IProjectService;
import com.polarion.platform.core.PlatformContext;
import com.polarion.subterra.base.location.ILocation;

/**
 * 检查单模板SVN存储仓库实现类
 * 使用Polarion SVN系统存储模板数据
 */
@Repository("checklistTemplateSvnRepository")
public class ChecklistTemplateSvnRepository implements ChecklistTemplateRepository {
    
    private static final Logger logger = LoggerFactory.getLogger(ChecklistTemplateSvnRepository.class);
    private static final String TEMPLATE_DIRECTORY = "checklist/template";
    private static final String FILE_EXTENSION = ".json";
    
    // 项目位置缓存，避免重复获取
    private final ConcurrentMap<String, ILocation> projectLocationCache = new ConcurrentHashMap<>();
    
    @Autowired
    private IRepositoryFileService repositoryFileService;
    
    /**
     * 获取项目根路径
     * 
     * @param projectId 项目ID
     * @return 项目根路径
     */
    private ILocation getProjectRootLocation(String projectId) {
        return projectLocationCache.computeIfAbsent(projectId, id -> {
            try {
                IProjectService projectService = PlatformContext.getPlatform().lookupService(IProjectService.class);
                return projectService.getProject(id).getLocation();
            } catch (Exception e) {
                logger.error("Failed to get project location for project: {}", id, e);
                throw new RuntimeException("无法获取项目路径: " + id, e);
            }
        });
    }
    
    /**
     * 构建模板文件路径
     * 
     * @param projectId 项目ID
     * @param templateId 模板ID
     * @return 文件路径
     */
    private ILocation getTemplateFileLocation(String projectId, String templateId) {
        return getTemplateDirectoryLocation(projectId).append(templateId + FILE_EXTENSION);
    }
    
    /**
     * 构建模板目录路径
     * 
     * @param projectId 项目ID
     * @return 目录路径
     */
    private ILocation getTemplateDirectoryLocation(String projectId) {
        ILocation projectRoot = getProjectRootLocation(projectId);
        return projectRoot.append(TEMPLATE_DIRECTORY);
    }
    
    /**
     * 生成新的模板ID
     * 
     * @return 新的模板ID
     */
    private String generateNewId() {
        return "template_" + UUID.randomUUID().toString().replace("-", "");
    }
    
    /**
     * 从输入流读取JSON内容并转换为模板对象
     * 
     * @param inputStream 输入流
     * @return 模板对象
     * @throws IOException IO异常
     */
    private ChecklistTemplate readTemplateFromStream(InputStream inputStream) throws IOException {
        if (inputStream == null) {
            return null;
        }
        
        try {
            byte[] bytes = inputStream.readAllBytes();
            String jsonContent = new String(bytes, StandardCharsets.UTF_8);
            if (jsonContent.trim().isEmpty()) {
                return null;
            }
            return JsonUtil.fromJson(jsonContent, ChecklistTemplate.class);
        } catch (Exception e) {
            throw new IOException("Failed to read template from stream", e);
        }
    }
    
    @Override
    public ChecklistTemplate save(String projectId, ChecklistTemplate entity) throws IOException {
        if (entity == null) {
            throw new IllegalArgumentException("模板不能为空");
        }
        if (projectId == null || projectId.trim().isEmpty()) {
            throw new IllegalArgumentException("项目ID不能为空");
        }
        
        // 如果没有ID，生成新ID
        if (entity.getId() == null || entity.getId().trim().isEmpty()) {
            entity.setId(generateNewId());
        }
        
        try {
            ILocation fileLocation = getTemplateFileLocation(projectId, entity.getId());
            String jsonContent = JsonUtil.toJson(entity);
            
            boolean success = repositoryFileService.writeFile(fileLocation, jsonContent);
            if (!success) {
                throw new IOException("Failed to save template: " + entity.getId());
            }
            
            return entity;
        } catch (Exception e) {
            logger.error("Failed to save template: {}", entity.getId(), e);
            throw new IOException("保存模板失败: " + entity.getId(), e);
        }
    }
    
    @Override
    public Optional<ChecklistTemplate> findById(String projectId, String id) throws IOException {
        if (id == null || id.trim().isEmpty()) {
            return Optional.empty();
        }
        if (projectId == null || projectId.trim().isEmpty()) {
            throw new IllegalArgumentException("项目ID不能为空");
        }
        
        try {
            ILocation fileLocation = getTemplateFileLocation(projectId, id);
            InputStream inputStream = repositoryFileService.readFile(fileLocation);
            ChecklistTemplate template = readTemplateFromStream(inputStream);
            return Optional.ofNullable(template);
        } catch (Exception e) {
            logger.error("Failed to find template by id: {}", id, e);
            throw new IOException("查找模板失败: " + id, e);
        }
    }
    
    @Override
    public List<ChecklistTemplate> findAll(String projectId) throws IOException {
        if (projectId == null || projectId.trim().isEmpty()) {
            throw new IllegalArgumentException("项目ID不能为空");
        }
        
        List<ChecklistTemplate> templates = new ArrayList<>();
        
        try {
            ILocation directoryLocation = getTemplateDirectoryLocation(projectId);
            List<String> fileNames = repositoryFileService.listFiles(directoryLocation);
            
            for (String fileName : fileNames) {
                if (fileName.endsWith(FILE_EXTENSION)) {
                    // 从文件名提取模板ID
                    String templateId = fileName.substring(0, fileName.length() - FILE_EXTENSION.length());
                    Optional<ChecklistTemplate> template = findById(projectId, templateId);
                    template.ifPresent(templates::add);
                }
            }
        } catch (Exception e) {
            logger.error("Failed to find all templates for project: {}", projectId, e);
            throw new IOException("查找所有模板失败", e);
        }
        
        return templates;
    }
    
    @Override
    public boolean existsById(String projectId, String id) throws IOException {
        if (id == null || id.trim().isEmpty()) {
            return false;
        }
        if (projectId == null || projectId.trim().isEmpty()) {
            throw new IllegalArgumentException("项目ID不能为空");
        }
        
        try {
            ILocation fileLocation = getTemplateFileLocation(projectId, id);
            return repositoryFileService.exists(fileLocation);
        } catch (Exception e) {
            logger.error("Failed to check if template exists: {}", id, e);
            return false;
        }
    }
    
    @Override
    public void deleteById(String projectId, String id) throws IOException {
        if (id == null || id.trim().isEmpty()) {
            return;
        }
        if (projectId == null || projectId.trim().isEmpty()) {
            throw new IllegalArgumentException("项目ID不能为空");
        }
        
        try {
            ILocation fileLocation = getTemplateFileLocation(projectId, id);
            boolean success = repositoryFileService.deleteFile(fileLocation);
            if (!success) {
                throw new IOException("Failed to delete template: " + id);
            }
        } catch (Exception e) {
            logger.error("Failed to delete template: {}", id, e);
            throw new IOException("删除模板失败: " + id, e);
        }
    }
    
    @Override
    public void delete(String projectId, ChecklistTemplate entity) throws IOException {
        if (entity == null) {
            return;
        }
        deleteById(projectId, entity.getId());
    }
    
    @Override
    public void deleteAll(String projectId) throws IOException {
        if (projectId == null || projectId.trim().isEmpty()) {
            throw new IllegalArgumentException("项目ID不能为空");
        }
        
        try {
            List<ChecklistTemplate> allTemplates = findAll(projectId);
            for (ChecklistTemplate template : allTemplates) {
                deleteById(projectId, template.getId());
            }
        } catch (Exception e) {
            logger.error("Failed to delete all templates for project: {}", projectId, e);
            throw new IOException("删除所有模板失败", e);
        }
    }
    
    @Override
    public long count(String projectId) throws IOException {
        if (projectId == null || projectId.trim().isEmpty()) {
            throw new IllegalArgumentException("项目ID不能为空");
        }

        try {
            return findAll(projectId).size();
        } catch (Exception e) {
            logger.error("Failed to count templates for project: {}", projectId, e);
            throw new IOException("统计模板数量失败", e);
        }
    }

    @Override
    public List<ChecklistTemplate> findByType(String projectId, String type) throws IOException {
        if (type == null || type.trim().isEmpty()) {
            throw new IllegalArgumentException("模板类型不能为空");
        }

        List<ChecklistTemplate> allTemplates = findAll(projectId);
        List<ChecklistTemplate> result = new ArrayList<>();

        for (ChecklistTemplate template : allTemplates) {
            if (type.equals(template.getType())) {
                result.add(template);
            }
        }

        return result;
    }

    @Override
    public Optional<ChecklistTemplate> findByName(String projectId, String name) throws IOException {
        if (name == null || name.trim().isEmpty()) {
            return Optional.empty();
        }

        List<ChecklistTemplate> allTemplates = findAll(projectId);

        for (ChecklistTemplate template : allTemplates) {
            if (name.equals(template.getName())) {
                return Optional.of(template);
            }
        }

        return Optional.empty();
    }

    @Override
    public Optional<ChecklistTemplate> findByTypeAndVersion(String projectId, String type, String version) throws IOException {
        if (type == null || type.trim().isEmpty() || version == null || version.trim().isEmpty()) {
            return Optional.empty();
        }

        List<ChecklistTemplate> allTemplates = findAll(projectId);

        for (ChecklistTemplate template : allTemplates) {
            if (type.equals(template.getType()) && version.equals(template.getVersion())) {
                return Optional.of(template);
            }
        }

        return Optional.empty();
    }

    @Override
    public Optional<ChecklistTemplate> findLatestByType(String projectId, String type) throws IOException {
        if (type == null || type.trim().isEmpty()) {
            return Optional.empty();
        }

        List<ChecklistTemplate> templates = findByType(projectId, type);
        if (templates.isEmpty()) {
            return Optional.empty();
        }

        // 按更新时间排序，获取最新的模板
        templates.sort((t1, t2) -> {
            if (t1.getUpdatedTime() == null && t2.getUpdatedTime() == null) {
                return 0;
            }
            if (t1.getUpdatedTime() == null) {
                return -1;
            }
            if (t2.getUpdatedTime() == null) {
                return 1;
            }
            return t2.getUpdatedTime().compareTo(t1.getUpdatedTime());
        });

        return Optional.of(templates.get(0));
    }

    @Override
    public boolean existsByName(String projectId, String name, String excludeId) throws IOException {
        if (name == null || name.trim().isEmpty()) {
            return false;
        }

        Optional<ChecklistTemplate> existing = findByName(projectId, name);
        if (!existing.isPresent()) {
            return false;
        }

        // 如果指定了排除ID，检查是否为同一个模板
        if (excludeId != null && excludeId.equals(existing.get().getId())) {
            return false;
        }

        return true;
    }

    @Override
    public boolean existsByName(String projectId, String name) throws IOException {
        return existsByName(projectId, name, null);
    }
}
