<template>
  <div class="item-config">
    <!-- Header -->
    <div class="config-header">
      <div class="header-left">
        <h3>检查项配置</h3>
        <p class="header-description">
          管理检查项的内容、分类和标签，支持拖拽排序和批量操作
        </p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="handleAddItem">
          <el-icon><Plus /></el-icon>
          添加检查项
        </el-button>
      </div>
    </div>

    <!-- Template selector -->
    <el-card class="selector-card" shadow="never">
      <template #header>
        <span class="card-title">选择模板</span>
      </template>
      
      <el-row :gutter="16">
        <el-col :span="8">
          <el-select
            v-model="selectedTemplateId"
            placeholder="请选择要配置的模板"
            filterable
            clearable
            style="width: 100%"
            @change="handleTemplateChange"
          >
            <el-option
              v-for="template in templates"
              :key="template.id"
              :label="`${template.name} (${template.type})`"
              :value="template.id"
            >
              <div class="template-option">
                <span class="template-name">{{ template.name }}</span>
                <el-tag size="small" type="primary">{{ template.type }}</el-tag>
                <el-badge :value="template.items?.length || 0" type="info" />
              </div>
            </el-option>
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-button @click="loadTemplates">
            <el-icon><Refresh /></el-icon>
            刷新模板
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- Items configuration -->
    <el-card v-if="currentTemplate" class="items-card" shadow="never">
      <template #header>
        <div class="card-header">
          <div class="header-info">
            <span class="card-title">检查项列表</span>
            <el-tag type="info">{{ filteredItems.length }} 项</el-tag>
          </div>
          <div class="header-actions">
            <el-button size="small" @click="handleBatchAdd">
              <el-icon><DocumentAdd /></el-icon>
              批量添加
            </el-button>
            <el-button
              size="small"
              type="danger"
              :disabled="selectedItems.length === 0"
              @click="handleBatchDelete"
            >
              <el-icon><Delete /></el-icon>
              批量删除 ({{ selectedItems.length }})
            </el-button>
          </div>
        </div>
      </template>

      <!-- Filters and search -->
      <div class="filters-bar">
        <el-row :gutter="16">
          <el-col :span="6">
            <el-input
              v-model="searchQuery"
              placeholder="搜索检查项内容"
              clearable
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="4">
            <el-select
              v-model="selectedCategory"
              placeholder="筛选分类"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="category in categories"
                :key="category"
                :label="category"
                :value="category"
              />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select
              v-model="requiredFilter"
              placeholder="筛选必填"
              clearable
              style="width: 100%"
            >
              <el-option label="必填项" value="required" />
              <el-option label="非必填项" value="optional" />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-button @click="clearFilters">
              <el-icon><RefreshLeft /></el-icon>
              清空筛选
            </el-button>
          </el-col>
        </el-row>
      </div>

      <!-- Items list with drag and drop -->
      <div class="items-container">
        <div class="list-header">
          <el-checkbox
            v-model="selectAll"
            :indeterminate="isIndeterminate"
            @change="handleSelectAll"
          >
            全选
          </el-checkbox>
          <span class="sort-hint">
            <el-icon><Rank /></el-icon>
            拖拽调整顺序
          </span>
        </div>

        <draggable
          v-model="currentTemplate.items"
          item-key="id"
          handle=".drag-handle"
          @end="handleDragEnd"
          class="draggable-list"
        >
          <template #item="{ element: item, index }">
            <div
              v-show="isItemVisible(item)"
              :class="['item-row', { 'selected': selectedItems.includes(item.id) }]"
            >
              <div class="item-content">
                <div class="item-left">
                  <el-checkbox
                    :model-value="selectedItems.includes(item.id)"
                    @change="(checked: boolean) => handleItemSelect(item.id, checked)"
                  />
                  <el-icon class="drag-handle"><Rank /></el-icon>
                  <span class="item-sequence">{{ getItemDisplayIndex(item) }}</span>
                </div>

                <div class="item-main">
                  <div class="item-info">
                    <div class="item-text">{{ item.content }}</div>
                    <div class="item-meta">
                      <el-tag v-if="item.category" size="small" type="info">
                        {{ item.category }}
                      </el-tag>
                      <el-tag v-if="item.required" size="small" type="danger">
                        必填
                      </el-tag>
                    </div>
                  </div>
                </div>

                <div class="item-actions">
                  <el-button size="small" text @click="handleEditItem(item)">
                    <el-icon><Edit /></el-icon>
                  </el-button>
                  <el-button size="small" text @click="handleDuplicateItem(item)">
                    <el-icon><CopyDocument /></el-icon>
                  </el-button>
                  <el-button size="small" text type="danger" @click="handleDeleteItem(item)">
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </div>
              </div>
            </div>
          </template>
        </draggable>

        <!-- Empty state -->
        <el-empty
          v-if="filteredItems.length === 0"
          :description="hasFilters ? '没有找到匹配的检查项' : '暂无检查项'"
        >
          <el-button v-if="!hasFilters" type="primary" @click="handleAddItem">
            添加第一个检查项
          </el-button>
        </el-empty>
      </div>

      <!-- Save button -->
      <div class="save-actions">
        <el-button
          type="primary"
          :loading="saving"
          @click="handleSaveTemplate"
        >
          <el-icon><Check /></el-icon>
          保存配置
        </el-button>
      </div>
    </el-card>

    <!-- Empty state for no template selected -->
    <el-empty
      v-else
      description="请先选择一个模板进行配置"
      :image-size="120"
    />

    <!-- Item form dialog -->
    <el-dialog
      v-model="itemFormVisible"
      :title="itemFormMode === 'create' ? '添加检查项' : '编辑检查项'"
      width="600px"
      :before-close="handleItemFormClose"
    >
      <el-form
        ref="itemFormRef"
        :model="itemFormData"
        :rules="itemFormRules"
        label-width="80px"
      >
        <el-form-item label="检查内容" prop="content">
          <el-input
            v-model="itemFormData.content"
            type="textarea"
            :rows="3"
            placeholder="请输入检查项内容"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="分类" prop="category">
          <el-select
            v-model="itemFormData.category"
            placeholder="请选择或输入分类"
            filterable
            allow-create
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="category in categories"
              :key="category"
              :label="category"
              :value="category"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="标签">
          <el-select
            v-model="itemFormData.tags"
            placeholder="请选择或输入标签"
            multiple
            filterable
            allow-create
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="tag in allTags"
              :key="tag"
              :label="tag"
              :value="tag"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="是否必填">
          <el-switch v-model="itemFormData.required" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="handleItemFormClose">取消</el-button>
        <el-button type="primary" @click="handleItemSave">
          {{ itemFormMode === 'create' ? '添加' : '保存' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- Batch add dialog -->
    <el-dialog
      v-model="batchAddVisible"
      title="批量添加检查项"
      width="700px"
      :before-close="handleBatchAddClose"
    >
      <div class="batch-add-content">
        <el-alert
          title="批量添加说明"
          type="info"
          :closable="false"
          style="margin-bottom: 16px"
        >
          <p>每行一个检查项，支持以下格式：</p>
          <p>• 简单格式：检查项内容</p>
          <p>• 带分类：[分类名称] 检查项内容</p>
          <p>• 必填项：检查项内容 *</p>
          <p>• 带标签：检查项内容 #标签1 #标签2</p>
          <p>• 完整格式：[分类名称] 检查项内容 #标签1 #标签2 *</p>
        </el-alert>
        
        <el-input
          v-model="batchAddText"
          type="textarea"
          :rows="10"
          placeholder="请输入检查项内容，每行一个..."
        />
        
        <div class="batch-preview" v-if="batchPreviewItems.length > 0">
          <h4>预览 ({{ batchPreviewItems.length }} 项)</h4>
          <div class="preview-list">
            <div
              v-for="(item, index) in batchPreviewItems"
              :key="index"
              class="preview-item"
            >
              <span class="preview-sequence">{{ index + 1 }}.</span>
              <span class="preview-content">{{ item.content }}</span>
              <el-tag v-if="item.category" size="small" type="info">
                {{ item.category }}
              </el-tag>
              <el-tag
                v-for="tag in item.tags"
                :key="tag"
                size="small"
                type="success"
              >
                {{ tag }}
              </el-tag>
              <el-tag v-if="item.required" size="small" type="danger">
                必填
              </el-tag>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="handleBatchAddClose">取消</el-button>
        <el-button
          type="primary"
          :disabled="batchPreviewItems.length === 0"
          @click="handleBatchAddConfirm"
        >
          添加 {{ batchPreviewItems.length }} 项
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import {
  Plus,
  Refresh,
  Search,
  RefreshLeft,
  Rank,
  Edit,
  Delete,
  CopyDocument,
  DocumentAdd,
  Check,
} from '@element-plus/icons-vue'
import draggable from 'vuedraggable'
import {
  getTemplates,
  updateTemplate,
  type ChecklistTemplate,
  type ChecklistItem,
} from '@/api/template'

// Route
const route = useRoute()

// Computed properties
const projectId = computed(() => {
  return route.query.projectId as string || 'default-project'
})

// Refs
const itemFormRef = ref<FormInstance>()

// Reactive data
const templates = ref<ChecklistTemplate[]>([])
const selectedTemplateId = ref('')
const currentTemplate = ref<ChecklistTemplate | null>(null)
const saving = ref(false)

// Search and filters
const searchQuery = ref('')
const selectedCategory = ref('')
const requiredFilter = ref('')

// Selection
const selectedItems = ref<string[]>([])

// Item form
const itemFormVisible = ref(false)
const itemFormMode = ref<'create' | 'edit'>('create')
const currentItem = ref<ChecklistItem | null>(null)

interface ItemFormData {
  content: string
  category: string
  tags: string[]
  required: boolean
}

const itemFormData = ref<ItemFormData>({
  content: '',
  category: '',
  tags: [],
  required: false,
})

const itemFormRules: FormRules = {
  content: [
    { required: true, message: '请输入检查项内容', trigger: 'blur' },
    { min: 1, max: 500, message: '检查项内容长度在 1 到 500 个字符', trigger: 'blur' },
  ],
}

// Batch add
const batchAddVisible = ref(false)
const batchAddText = ref('')

// Computed properties
const categories = computed(() => {
  if (!currentTemplate.value) return []
  const cats = new Set<string>()
  currentTemplate.value.items.forEach(item => {
    if (item.category) cats.add(item.category)
  })
  return Array.from(cats).sort()
})

const allTags = computed(() => {
  if (!currentTemplate.value) return []
  const tags = new Set<string>()
  currentTemplate.value.items.forEach(item => {
    if (item.tags) {
      item.tags.forEach(tag => tags.add(tag))
    }
  })
  return Array.from(tags).sort()
})

const filteredItems = computed(() => {
  if (!currentTemplate.value) return []
  
  return currentTemplate.value.items.filter(item => {
    // Search filter
    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase()
      if (!item.content.toLowerCase().includes(query)) {
        return false
      }
    }
    
    // Category filter
    if (selectedCategory.value) {
      if (item.category !== selectedCategory.value) {
        return false
      }
    }
    
    // Required filter
    if (requiredFilter.value) {
      if (requiredFilter.value === 'required' && !item.required) {
        return false
      }
      if (requiredFilter.value === 'optional' && item.required) {
        return false
      }
    }
    
    return true
  })
})

const hasFilters = computed(() => {
  return !!(searchQuery.value || selectedCategory.value || requiredFilter.value)
})

const selectAll = computed({
  get: () => {
    const visibleIds = filteredItems.value.map(item => item.id)
    return visibleIds.length > 0 && visibleIds.every(id => selectedItems.value.includes(id))
  },
  set: (value: boolean) => {
    if (value) {
      const visibleIds = filteredItems.value.map(item => item.id)
      selectedItems.value = [...new Set([...selectedItems.value, ...visibleIds])]
    } else {
      const visibleIds = new Set(filteredItems.value.map(item => item.id))
      selectedItems.value = selectedItems.value.filter(id => !visibleIds.has(id))
    }
  }
})

const isIndeterminate = computed(() => {
  const visibleIds = filteredItems.value.map(item => item.id)
  const selectedVisibleIds = selectedItems.value.filter(id => visibleIds.includes(id))
  return selectedVisibleIds.length > 0 && selectedVisibleIds.length < visibleIds.length
})

const batchPreviewItems = computed(() => {
  if (!batchAddText.value.trim()) return []
  
  const lines = batchAddText.value.split('\n').filter(line => line.trim())
  return lines.map(line => {
    const trimmed = line.trim()
    let content = trimmed
    let category = ''
    let tags: string[] = []
    let required = false
    
    // Check for required marker (*)
    if (trimmed.endsWith(' *')) {
      required = true
      content = trimmed.slice(0, -2).trim()
    }
    
    // Extract tags (#tag)
    const tagMatches = content.match(/#(\w+)/g)
    if (tagMatches) {
      tags = tagMatches.map(match => match.slice(1))
      content = content.replace(/#\w+/g, '').trim()
    }
    
    // Check for category [category]
    const categoryMatch = content.match(/^\[([^\]]+)\]\s*(.+)$/)
    if (categoryMatch) {
      category = categoryMatch[1].trim()
      content = categoryMatch[2].trim()
    }
    
    return {
      content,
      category,
      tags,
      required,
    }
  }).filter(item => item.content)
})

// Methods
const loadTemplates = async () => {
  try {
    const response = await getTemplates(projectId.value)
    templates.value = response || []
  } catch (error) {
    console.error('Failed to load templates:', error)
    ElMessage.error('加载模板列表失败')
  }
}

const handleTemplateChange = async (templateId: string) => {
  if (!templateId) {
    currentTemplate.value = null
    selectedItems.value = []
    return
  }
  
  const template = templates.value.find(t => t.id === templateId)
  if (template) {
    // Deep clone to avoid modifying original data
    currentTemplate.value = JSON.parse(JSON.stringify(template))
    selectedItems.value = []
  }
}

const isItemVisible = (item: ChecklistItem) => {
  return filteredItems.value.some(filteredItem => filteredItem.id === item.id)
}

const getItemDisplayIndex = (item: ChecklistItem) => {
  if (!currentTemplate.value) return 0
  return currentTemplate.value.items.findIndex(i => i.id === item.id) + 1
}

const handleItemSelect = (itemId: string, checked: boolean) => {
  if (checked) {
    if (!selectedItems.value.includes(itemId)) {
      selectedItems.value.push(itemId)
    }
  } else {
    selectedItems.value = selectedItems.value.filter(id => id !== itemId)
  }
}

const handleSelectAll = (checked: boolean) => {
  selectAll.value = checked
}

const clearFilters = () => {
  searchQuery.value = ''
  selectedCategory.value = ''
  requiredFilter.value = ''
}

const handleAddItem = () => {
  itemFormData.value = {
    content: '',
    category: '',
    tags: [],
    required: false,
  }
  itemFormMode.value = 'create'
  currentItem.value = null
  itemFormVisible.value = true
}

const handleEditItem = (item: ChecklistItem) => {
  itemFormData.value = {
    content: item.content,
    category: item.category || '',
    tags: item.tags || [],
    required: item.required,
  }
  itemFormMode.value = 'edit'
  currentItem.value = item
  itemFormVisible.value = true
}

const handleDuplicateItem = (item: ChecklistItem) => {
  if (!currentTemplate.value) return
  
  const newItem: ChecklistItem = {
    id: `item_${Date.now()}_${Math.random()}`,
    sequence: currentTemplate.value.items.length + 1,
    content: `${item.content} - 副本`,
    category: item.category,
    tags: item.tags ? [...item.tags] : [],
    required: item.required,
  }
  
  currentTemplate.value.items.push(newItem)
  ElMessage.success('检查项复制成功')
}

const handleDeleteItem = async (item: ChecklistItem) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除检查项 "${item.content}" 吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    if (!currentTemplate.value) return
    
    const index = currentTemplate.value.items.findIndex(i => i.id === item.id)
    if (index > -1) {
      currentTemplate.value.items.splice(index, 1)
      selectedItems.value = selectedItems.value.filter(id => id !== item.id)
      ElMessage.success('检查项删除成功')
    }
  } catch (error) {
    // User cancelled
  }
}

const handleBatchDelete = async () => {
  if (selectedItems.value.length === 0) return
  
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedItems.value.length} 个检查项吗？此操作不可恢复。`,
      '批量删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    if (!currentTemplate.value) return
    
    currentTemplate.value.items = currentTemplate.value.items.filter(
      item => !selectedItems.value.includes(item.id)
    )
    
    ElMessage.success(`成功删除 ${selectedItems.value.length} 个检查项`)
    selectedItems.value = []
  } catch (error) {
    // User cancelled
  }
}

const handleItemFormClose = () => {
  itemFormVisible.value = false
}

const handleItemSave = async () => {
  if (!itemFormRef.value || !currentTemplate.value) return
  
  try {
    const valid = await itemFormRef.value.validate()
    if (!valid) return
    
    if (itemFormMode.value === 'create') {
      const newItem: ChecklistItem = {
        id: `item_${Date.now()}_${Math.random()}`,
        sequence: currentTemplate.value.items.length + 1,
        content: itemFormData.value.content,
        category: itemFormData.value.category,
        tags: itemFormData.value.tags,
        required: itemFormData.value.required,
      }
      
      currentTemplate.value.items.push(newItem)
      ElMessage.success('检查项添加成功')
    } else if (currentItem.value) {
      const index = currentTemplate.value.items.findIndex(i => i.id === currentItem.value!.id)
      if (index > -1) {
        currentTemplate.value.items[index] = {
          ...currentTemplate.value.items[index],
          content: itemFormData.value.content,
          category: itemFormData.value.category,
          tags: itemFormData.value.tags,
          required: itemFormData.value.required,
        }
        ElMessage.success('检查项更新成功')
      }
    }
    
    itemFormVisible.value = false
  } catch (error) {
    console.error('Failed to save item:', error)
  }
}

const handleBatchAdd = () => {
  batchAddText.value = ''
  batchAddVisible.value = true
}

const handleBatchAddClose = () => {
  batchAddVisible.value = false
}

const handleBatchAddConfirm = () => {
  if (!currentTemplate.value) return
  
  const newItems = batchPreviewItems.value.map((item, index) => ({
    id: `item_${Date.now()}_${index}`,
    sequence: currentTemplate.value!.items.length + index + 1,
    content: item.content,
    category: item.category,
    tags: item.tags,
    required: item.required,
  }))
  
  currentTemplate.value.items.push(...newItems)
  ElMessage.success(`成功添加 ${newItems.length} 个检查项`)
  batchAddVisible.value = false
}

const handleDragEnd = () => {
  if (!currentTemplate.value) return
  
  // Update sequence numbers
  currentTemplate.value.items.forEach((item, index) => {
    item.sequence = index + 1
  })
  
  ElMessage.success('检查项顺序已更新')
}

const handleSaveTemplate = async () => {
  if (!currentTemplate.value) return
  
  try {
    saving.value = true
    
    // Prepare items data with updated sequences
    const items = currentTemplate.value.items.map((item, index) => ({
      ...item,
      sequence: index + 1,
    }))
    
    await updateTemplate(projectId.value, currentTemplate.value.id, { items })
    ElMessage.success('模板配置保存成功')
    
    // Reload templates to get updated data
    await loadTemplates()
  } catch (error) {
    console.error('Failed to save template:', error)
    ElMessage.error('保存失败，请稍后重试')
  } finally {
    saving.value = false
  }
}

// Lifecycle
onMounted(() => {
  loadTemplates()
})
</script>

<style scoped>
.item-config {
  padding: 20px;
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.header-left h3 {
  margin: 0 0 8px 0;
  color: #303133;
}

.header-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.selector-card,
.items-card {
  margin-bottom: 20px;
}

.card-title {
  font-weight: 600;
  color: #303133;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.header-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.template-option {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.template-name {
  flex: 1;
}

.filters-bar {
  margin-bottom: 20px;
}

.items-container {
  min-height: 200px;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f5f7fa;
  border-radius: 6px;
  margin-bottom: 12px;
}

.sort-hint {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #909399;
  font-size: 12px;
}

.draggable-list {
  min-height: 100px;
}

.item-row {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  margin-bottom: 8px;
  background: white;
  transition: all 0.2s;
}

.item-row:hover {
  border-color: #409eff;
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.1);
}

.item-row.selected {
  border-color: #409eff;
  background: #f0f9ff;
}

.item-content {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  gap: 12px;
}

.item-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.drag-handle {
  cursor: move;
  color: #909399;
}

.item-sequence {
  font-weight: 600;
  color: #606266;
  min-width: 24px;
  text-align: center;
}

.item-main {
  flex: 1;
}

.item-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.item-text {
  color: #303133;
  line-height: 1.5;
}

.item-meta {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.item-actions {
  display: flex;
  gap: 4px;
}

.save-actions {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}

.batch-add-content {
  max-height: 60vh;
  overflow-y: auto;
}

.batch-preview {
  margin-top: 16px;
  border-top: 1px solid #e4e7ed;
  padding-top: 16px;
}

.batch-preview h4 {
  margin: 0 0 12px 0;
  color: #303133;
}

.preview-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 8px;
  background: #fafafa;
}

.preview-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
  font-size: 14px;
}

.preview-sequence {
  color: #909399;
  min-width: 20px;
}

.preview-content {
  flex: 1;
  color: #303133;
}

:deep(.el-card__header) {
  padding: 16px 20px;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-form-item) {
  margin-bottom: 18px;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-empty) {
  padding: 40px 0;
}
</style>